#!/usr/bin/env python3
"""
Modem分区分析工具 - 专门用于Yocto Linux系统中nr_modem的分区和加载地址分析
支持UBIFS文件系统和U-Boot固件分析
"""

import struct
import sys
import os
import hashlib
import re
from typing import Dict, List, Tuple, Optional

class ModemPartitionAnalyzer:
    """Modem分区分析器"""
    
    def __init__(self, firmware_file: str):
        """初始化分析器"""
        self.firmware_file = firmware_file
        self.data = None
        self.payload_size = 0
        self.cert_offset = 0
        self.partition_table = {}
        self.modem_info = {}
        
        print(f"🔍 初始化Modem分区分析器")
        print(f"目标固件: {firmware_file}")
        
    def load_firmware(self) -> bool:
        """加载固件文件"""
        try:
            with open(self.firmware_file, 'rb') as f:
                self.data = f.read()
            
            print(f"✅ 固件加载成功，大小: {len(self.data):,} 字节 (0x{len(self.data):x})")
            
            # 解析基础结构
            if len(self.data) >= 52:
                self.payload_size = struct.unpack('<I', self.data[48:52])[0]
                print(f"📦 Payload大小: {self.payload_size:,} 字节 (0x{self.payload_size:x})")
                
                # 计算证书偏移
                cert_offset_pos = self.payload_size + 552
                if cert_offset_pos + 8 <= len(self.data):
                    self.cert_offset = struct.unpack('<Q', self.data[cert_offset_pos:cert_offset_pos + 8])[0]
                    print(f"📜 证书偏移: 0x{self.cert_offset:x}")
                
            return True
            
        except Exception as e:
            print(f"❌ 固件加载失败: {e}")
            return False
    
    def search_modem_signatures(self) -> List[Dict]:
        """搜索modem相关的签名和字符串"""
        print(f"\n🔍 搜索Modem相关签名...")
        
        modem_patterns = [
            # NR Modem相关
            (b'nr_modem', 'NR Modem标识'),
            (b'NR_MODEM', 'NR Modem标识(大写)'),
            (b'5G_MODEM', '5G Modem标识'),
            (b'modem_fw', 'Modem固件标识'),
            (b'MODEM_FW', 'Modem固件标识(大写)'),
            
            # 分区相关
            (b'modem', 'Modem分区'),
            (b'MODEM', 'Modem分区(大写)'),
            (b'radio', 'Radio分区'),
            (b'RADIO', 'Radio分区(大写)'),
            (b'baseband', 'Baseband分区'),
            (b'BASEBAND', 'Baseband分区(大写)'),
            
            # 设备树相关
            (b'qcom,msm-id', 'Qualcomm设备ID'),
            (b'qcom,board-id', 'Qualcomm板级ID'),
            (b'modem-subsys', 'Modem子系统'),
            (b'mss-pil', 'Modem Subsystem PIL'),
            
            # 地址和加载相关
            (b'load_addr', '加载地址'),
            (b'entry_point', '入口点'),
            (b'mem_base', '内存基址'),
            (b'ddr_base', 'DDR基址'),
        ]
        
        findings = []
        
        for pattern, description in modem_patterns:
            offset = 0
            while True:
                pos = self.data.find(pattern, offset)
                if pos == -1:
                    break
                
                # 获取上下文
                context_start = max(0, pos - 32)
                context_end = min(len(self.data), pos + len(pattern) + 32)
                context = self.data[context_start:context_end]
                
                finding = {
                    'pattern': pattern.decode('utf-8', errors='ignore'),
                    'description': description,
                    'offset': pos,
                    'hex_offset': f"0x{pos:x}",
                    'context': context.hex(),
                    'ascii_context': ''.join(chr(b) if 32 <= b <= 126 else '.' for b in context)
                }
                
                findings.append(finding)
                print(f"  ✅ 找到 {description}: 0x{pos:x}")
                
                offset = pos + 1
        
        return findings
    
    def analyze_partition_table(self) -> Dict:
        """分析分区表结构"""
        print(f"\n📋 分析分区表结构...")
        
        # 搜索常见的分区表签名
        partition_signatures = [
            (b'ANDROID!', 'Android Boot Image'),
            (b'\x55\xAA', 'MBR签名'),
            (b'EFI PART', 'GPT分区表'),
            (b'QCOM', 'Qualcomm分区表'),
            (b'MIBIB', 'Qualcomm MIBIB'),
        ]
        
        partition_info = {}
        
        for signature, description in partition_signatures:
            pos = self.data.find(signature)
            if pos != -1:
                print(f"  ✅ 找到 {description}: 0x{pos:x}")
                partition_info[description] = {
                    'offset': pos,
                    'signature': signature.hex()
                }
        
        # 搜索可能的分区名称
        partition_names = [
            b'boot', b'recovery', b'system', b'userdata', b'cache',
            b'modem', b'radio', b'baseband', b'dsp', b'adsp',
            b'tz', b'rpm', b'sbl1', b'sbl2', b'sbl3',
            b'aboot', b'laf', b'misc', b'persist'
        ]
        
        for name in partition_names:
            pos = self.data.find(name)
            if pos != -1:
                # 尝试解析周围的数据作为分区信息
                context_start = max(0, pos - 64)
                context_end = min(len(self.data), pos + 64)
                context = self.data[context_start:context_end]
                
                # 查找可能的地址和大小信息
                addresses = []
                for i in range(0, len(context) - 8, 4):
                    try:
                        addr = struct.unpack('<I', context[i:i+4])[0]
                        if 0x80000000 <= addr <= 0xFFFFFFFF:  # 可能的内存地址
                            addresses.append((context_start + i, addr))
                    except:
                        pass
                
                partition_info[name.decode()] = {
                    'name_offset': pos,
                    'possible_addresses': addresses[:5]  # 只保留前5个可能的地址
                }
        
        return partition_info
    
    def search_load_addresses(self) -> List[Dict]:
        """搜索可能的加载地址"""
        print(f"\n🎯 搜索可能的加载地址...")
        
        load_addresses = []
        
        # 常见的ARM64内存布局地址范围
        address_ranges = [
            (0x80000000, 0x9FFFFFFF, "DDR低地址段"),
            (0xA0000000, 0xBFFFFFFF, "DDR中地址段"),
            (0xC0000000, 0xDFFFFFFF, "DDR高地址段"),
            (0x40000000, 0x7FFFFFFF, "外设地址段"),
        ]
        
        # 搜索32位地址
        for i in range(0, len(self.data) - 4, 4):
            try:
                addr = struct.unpack('<I', self.data[i:i+4])[0]
                
                for start, end, description in address_ranges:
                    if start <= addr <= end:
                        # 检查是否在重要位置附近
                        if (abs(i - self.payload_size) < 1024 or 
                            abs(i - self.cert_offset) < 1024 or
                            i < 1024):  # 文件头部
                            
                            load_addresses.append({
                                'offset': i,
                                'hex_offset': f"0x{i:x}",
                                'address': addr,
                                'hex_address': f"0x{addr:x}",
                                'range_description': description,
                                'context': 'payload' if abs(i - self.payload_size) < 1024 else
                                          'cert' if abs(i - self.cert_offset) < 1024 else 'header'
                            })
                            
            except:
                continue
        
        # 按地址排序并去重
        unique_addresses = {}
        for addr_info in load_addresses:
            key = addr_info['address']
            if key not in unique_addresses:
                unique_addresses[key] = addr_info
        
        sorted_addresses = sorted(unique_addresses.values(), key=lambda x: x['address'])
        
        print(f"  📍 找到 {len(sorted_addresses)} 个可能的加载地址")
        for addr_info in sorted_addresses[:10]:  # 显示前10个
            print(f"    0x{addr_info['address']:08x} @ 0x{addr_info['offset']:x} ({addr_info['range_description']})")
        
        return sorted_addresses
    
    def analyze_device_tree_info(self) -> Dict:
        """分析设备树信息"""
        print(f"\n🌳 分析设备树信息...")
        
        dt_info = {}
        
        # 搜索设备树相关的字符串
        dt_patterns = [
            b'/memory@',
            b'/reserved-memory',
            b'/soc/',
            b'qcom,',
            b'compatible',
            b'reg = <',
            b'#address-cells',
            b'#size-cells',
        ]
        
        for pattern in dt_patterns:
            positions = []
            offset = 0
            while True:
                pos = self.data.find(pattern, offset)
                if pos == -1:
                    break
                positions.append(pos)
                offset = pos + 1
                if len(positions) >= 5:  # 限制数量
                    break
            
            if positions:
                dt_info[pattern.decode('utf-8', errors='ignore')] = positions
        
        return dt_info
    
    def generate_analysis_report(self) -> str:
        """生成分析报告"""
        print(f"\n📝 生成分析报告...")
        
        # 执行所有分析
        modem_signatures = self.search_modem_signatures()
        partition_table = self.analyze_partition_table()
        load_addresses = self.search_load_addresses()
        dt_info = self.analyze_device_tree_info()
        
        report = f"""# Modem分区和加载地址分析报告

## 基础信息
- **固件文件**: {self.firmware_file}
- **文件大小**: {len(self.data):,} 字节 (0x{len(self.data):x})
- **Payload大小**: {self.payload_size:,} 字节 (0x{self.payload_size:x})
- **证书偏移**: 0x{self.cert_offset:x}

## Modem相关签名发现
"""
        
        if modem_signatures:
            for sig in modem_signatures:
                report += f"- **{sig['description']}**: 0x{sig['offset']:x}\n"
                report += f"  - 模式: `{sig['pattern']}`\n"
                report += f"  - ASCII上下文: `{sig['ascii_context']}`\n\n"
        else:
            report += "❌ 未找到明显的Modem相关签名\n\n"
        
        report += "## 分区表分析\n"
        if partition_table:
            for name, info in partition_table.items():
                report += f"- **{name}**: 0x{info.get('offset', 'N/A'):x}\n"
                if 'possible_addresses' in info:
                    for addr_offset, addr in info['possible_addresses']:
                        report += f"  - 可能地址: 0x{addr:08x} @ 0x{addr_offset:x}\n"
        else:
            report += "❌ 未找到明显的分区表结构\n\n"
        
        report += "## 可能的加载地址\n"
        for addr_info in load_addresses[:20]:  # 显示前20个
            report += f"- **0x{addr_info['address']:08x}** @ 0x{addr_info['offset']:x} ({addr_info['range_description']})\n"
        
        return report

def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("用法: python modem_partition_analyzer.py <固件文件>")
        print("示例: python modem_partition_analyzer.py uboot")
        return 1
    
    firmware_file = sys.argv[1]
    
    if not os.path.exists(firmware_file):
        print(f"❌ 文件不存在: {firmware_file}")
        return 1
    
    print("🚀 Modem分区分析工具 - Yocto Linux/UBIFS专用")
    print("=" * 60)
    
    # 创建分析器
    analyzer = ModemPartitionAnalyzer(firmware_file)
    
    # 加载固件
    if not analyzer.load_firmware():
        return 1
    
    # 生成报告
    report = analyzer.generate_analysis_report()
    
    # 保存报告
    report_file = f"{firmware_file}_modem_analysis.md"
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n✅ 分析完成！")
    print(f"📄 报告已保存到: {report_file}")
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
